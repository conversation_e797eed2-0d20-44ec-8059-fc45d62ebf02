import React, { useEffect, useState } from 'react'
import { useNetworkStore } from '../stores/networkStore'
import LocalModelSetupWizard from './LocalModelSetupWizard'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faWifi, faBan, faInfoCircle, faCircle, faCheck } from '@fortawesome/free-solid-svg-icons'

interface NetworkStatusProps {
  className?: string
}

const NetworkStatus: React.FC<NetworkStatusProps> = ({ className = '' }) => {
  const [showSetupWizard, setShowSetupWizard] = useState(false)
  const {
    isOnline,
    isPrivateMode,
    localModelsAvailable,
    ollamaConnected,
    lmStudioConnected,
    toggleOnline,
    togglePrivateMode,
    checkLocalModels
  } = useNetworkStore()

  // Check local models on component mount and when private mode changes
  useEffect(() => {
    // Always check for local models on mount
    checkLocalModels()

    // Set up periodic check every 30 seconds
    const interval = setInterval(checkLocalModels, 30000)

    return () => clearInterval(interval)
  }, [checkLocalModels])

  // Additional check when private mode changes
  useEffect(() => {
    if (isPrivateMode) {
      checkLocalModels()
    }
  }, [isPrivateMode, checkLocalModels])

  const getStatusText = () => {
    if (isPrivateMode) {
      return 'Offline'
    }
    return 'Online'
  }

  const getStatusColor = () => {
    if (isPrivateMode) {
      return 'text-gray-300' // Dark color when in private mode
    }
    return 'text-primary' // Online color when private mode is off
  }

  const getWifiIconColor = () => {
    if (isPrivateMode) {
      return 'text-gray-300' // Light gray for offline
    }
    return 'text-primary' // Normal color when private mode is off
  }

  const getWifiIcon = () => {
    return isPrivateMode ? faBan : faWifi
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Network Status Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={toggleOnline}
            className="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors"
            title={isOnline ? 'Go Offline' : 'Go Online'}
          >
            <FontAwesomeIcon icon={getWifiIcon()} className={`text-sm ${getWifiIconColor()}`} />
          </button>
          <div className="flex flex-col">
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>
        </div>
      </div>

      {/* Private Mode Toggle */}
      <div className="bg-gray-700/50 rounded-lg p-3">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-supplement1">Private Mode</span>
          <button
            onClick={togglePrivateMode}
            className={`
              relative inline-flex h-6 w-11 items-center rounded-full transition-colors
              ${isPrivateMode ? 'bg-secondary' : 'bg-gray-600'}
            `}
            title="Toggle Private Mode"
          >
            <span
              className={`
                inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                ${isPrivateMode ? 'translate-x-6' : 'translate-x-1'}
              `}
            />
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={faInfoCircle} className="text-gray-400 text-xs" />
          <span className="text-xs text-gray-400">
            {isPrivateMode
              ? 'Local models only. All documents stay private.'
              : 'External models available. Data may be shared.'
            }
          </span>
        </div>

        {/* Local Models Status - Show in both modes */}
        <div className="mt-2 pt-2 border-t border-gray-600">
          <div className="flex items-center gap-2 mb-1">
            <FontAwesomeIcon icon={faCircle} className={`text-xs ${localModelsAvailable ? 'text-green-400' : 'text-red-400'}`} />
            <span className="text-xs font-medium text-supplement1">
              Local Models: {localModelsAvailable ? 'Available' : 'Not Found'}
            </span>
          </div>

          {localModelsAvailable ? (
            <div className="space-y-1">
              {ollamaConnected && (
                <div className="flex items-center gap-2">
                  <FontAwesomeIcon icon={faCheck} className="text-xs text-green-400" />
                  <span className="text-xs text-gray-300">Ollama Connected</span>
                </div>
              )}
              {lmStudioConnected && (
                <div className="flex items-center gap-2">
                  <FontAwesomeIcon icon={faCheck} className="text-xs text-green-400" />
                  <span className="text-xs text-gray-300">LM Studio Connected</span>
                </div>
              )}
            </div>
          ) : (
            <button
              onClick={() => setShowSetupWizard(true)}
              className="text-xs text-primary hover:text-primary/80 underline"
            >
              Setup Local Models
            </button>
          )}
        </div>
      </div>

      {/* Setup Wizard Modal */}
      <LocalModelSetupWizard
        isOpen={showSetupWizard}
        onClose={() => setShowSetupWizard(false)}
      />
    </div>
  )
}

export default NetworkStatus
